import type { NewsItem } from "~/shared/types"
import { load } from "cheerio"

export default defineEventHandler(async (): Promise<NewsItem[]> => {
  try {
    const response = await $fetch("https://www.exosomemed.com/", {
      headers: {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
      },
    })

    const $ = load(response)
    const items: NewsItem[] = []

    // 外泌体和细胞外囊泡相关关键词
    const keywords = [
      "外泌体", "细胞外囊泡", "细胞外膜泡", "EVs", "exosome", "extracellular vesicles",
      "微囊泡", "凋亡小体", "胞外囊泡", "囊泡", "纳米颗粒", "生物标志物",
      "液体活检", "诊断", "治疗", "药物递送", "靶向治疗", "细胞通讯",
      "肿瘤", "癌症", "免疫", "炎症", "神经", "心血管", "干细胞", "再生医学",
      "分离", "纯化", "检测", "表征", "功能", "机制", "载药", "工程化",
      "临床", "研究", "发现", "突破", "创新", "论文", "发表", "Nature", "Science", "Cell",
      "蛋白质组", "RNA", "miRNA", "lncRNA", "DNA", "脂质", "代谢物",
      "生物医学", "精准医学", "个性化医疗", "转化医学", "基础研究", "应用研究"
    ]

    // 尝试多种选择器策略
    const selectors = [
      "article",
      ".post",
      ".entry",
      ".news-item",
      ".article-item",
      ".content-item",
      "h1 a, h2 a, h3 a",
      ".entry-title a",
      ".post-title a"
    ]

    let newsFound = false

    for (const selector of selectors) {
      const elements = $(selector)
      if (elements.length > 0) {
        elements.each((_, element) => {
          try {
            const $item = $(element)
            
            // 提取标题和链接
            let title = ""
            let url = ""
            
            if ($item.is("a")) {
              title = $item.text().trim()
              url = $item.attr("href") || ""
            } else {
              // 查找标题
              const titleSelectors = ["h1", "h2", "h3", "h4", ".title", ".headline", ".entry-title", ".post-title"]
              for (const titleSel of titleSelectors) {
                const titleEl = $item.find(titleSel).first()
                if (titleEl.length > 0) {
                  const linkEl = titleEl.find("a").first()
                  if (linkEl.length > 0) {
                    title = linkEl.text().trim()
                    url = linkEl.attr("href") || ""
                  } else {
                    title = titleEl.text().trim()
                  }
                  if (title) break
                }
              }
              
              // 如果没有找到标题，查找链接
              if (!url) {
                const linkEl = $item.find("a").first()
                if (linkEl.length > 0) {
                  url = linkEl.attr("href") || ""
                  if (!title) {
                    title = linkEl.text().trim()
                  }
                }
              }
            }

            if (!title || title.length < 10 || !url) return

            // 关键词过滤
            const hasKeyword = keywords.some(keyword => 
              title.toLowerCase().includes(keyword.toLowerCase())
            )
            if (!hasKeyword) return

            // 处理相对URL
            if (url && !url.startsWith("http")) {
              url = url.startsWith("/") ? `https://www.exosomemed.com${url}` : `https://www.exosomemed.com/${url}`
            }

            // 提取时间
            let time = ""
            const timeSelectors = [".time", ".date", ".publish-time", ".created", "time", ".meta-time", ".post-date", ".entry-date"]
            for (const timeSel of timeSelectors) {
              const timeEl = $item.find(timeSel).first()
              if (timeEl.length > 0) {
                time = timeEl.text().trim()
                if (time) break
              }
            }

            // 从文本中提取时间
            if (!time) {
              const text = $item.text()
              const timeMatch = text.match(/(\d{4}[-\/]\d{1,2}[-\/]\d{1,2})/);
              if (timeMatch) {
                time = timeMatch[1]
              }
            }

            if (title && url) {
              items.push({
                title: title.substring(0, 100),
                url,
                time: time || new Date().toLocaleDateString("zh-CN"),
              })
              newsFound = true
            }
          } catch (error) {
            console.error("外泌体之家单项解析错误:", error)
          }
        })

        if (newsFound) break
      }
    }

    // 如果没有找到新闻，尝试通用选择器
    if (!newsFound) {
      $("a").each((_, element) => {
        try {
          const $link = $(element)
          const title = $link.text().trim()
          const url = $link.attr("href")

          if (title && url && title.length > 10) {
            const hasKeyword = keywords.some(keyword => 
              title.toLowerCase().includes(keyword.toLowerCase())
            )
            
            if (hasKeyword) {
              let fullUrl = url
              if (!fullUrl.startsWith("http")) {
                fullUrl = fullUrl.startsWith("/") ? `https://www.exosomemed.com${fullUrl}` : `https://www.exosomemed.com/${fullUrl}`
              }

              items.push({
                title: title.substring(0, 100),
                url: fullUrl,
                time: new Date().toLocaleDateString("zh-CN"),
              })
            }
          }
        } catch (error) {
          console.error("外泌体之家通用解析错误:", error)
        }
      })
    }

    console.log(`外泌体之家获取到 ${items.length} 条新闻`)
    return items.slice(0, 20) // 限制返回数量

  } catch (error) {
    console.error("外泌体之家获取失败:", error)
    return []
  }
})
