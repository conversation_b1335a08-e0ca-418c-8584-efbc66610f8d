import type { NewsItem } from "~/shared/types"
import { load } from "cheerio"

export default defineEventHandler(async (): Promise<NewsItem[]> => {
  try {
    const response = await $fetch("https://www.caivd-org.cn/hangye.asp", {
      headers: {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
      },
    })

    const $ = load(response)
    const items: NewsItem[] = []

    // 体外诊断行业相关关键词
    const keywords = [
      "体外诊断", "IVD", "医疗器械", "诊断", "检测", "试剂", "仪器", "设备",
      "行业", "市场", "产业", "企业", "公司", "发展", "趋势", "政策", "法规",
      "生物标志物", "分子诊断", "免疫诊断", "生化诊断", "血液诊断", "微生物诊断",
      "POCT", "即时检测", "快速检测", "精准医疗", "个性化医疗", "液体活检",
      "基因检测", "蛋白检测", "抗体检测", "病原体检测", "肿瘤标志物",
      "临床检验", "实验室", "质控", "标准化", "认证", "注册", "审批",
      "FDA", "NMPA", "CE", "ISO", "CLIA", "CAP", "质量管理",
      "创新", "技术", "平台", "产品", "研发", "上市", "投资", "并购", "融资"
    ]

    // 尝试多种选择器策略
    const selectors = [
      "li a[href*='article.asp']",
      ".news-list li",
      ".article-list li", 
      ".content-list li",
      "ul li a",
      "li",
      "a[href*='article']"
    ]

    let newsFound = false

    for (const selector of selectors) {
      const elements = $(selector)
      if (elements.length > 0) {
        elements.each((_, element) => {
          try {
            const $item = $(element)
            
            // 提取标题和链接
            let title = ""
            let url = ""
            
            if ($item.is("a")) {
              title = $item.text().trim()
              url = $item.attr("href") || ""
            } else {
              const linkEl = $item.find("a").first()
              if (linkEl.length > 0) {
                title = linkEl.text().trim()
                url = linkEl.attr("href") || ""
              }
            }

            if (!title || title.length < 10 || !url) return

            // 关键词过滤
            const hasKeyword = keywords.some(keyword => 
              title.toLowerCase().includes(keyword.toLowerCase())
            )
            if (!hasKeyword) return

            // 处理相对URL
            if (url && !url.startsWith("http")) {
              url = url.startsWith("/") ? `https://www.caivd-org.cn${url}` : `https://www.caivd-org.cn/${url}`
            }

            // 提取时间
            let time = ""
            const timeSelectors = [".time", ".date", ".publish-time", ".created", "time"]
            for (const timeSel of timeSelectors) {
              const timeEl = $item.find(timeSel).first()
              if (timeEl.length > 0) {
                time = timeEl.text().trim()
                if (time) break
              }
            }

            // 从文本中提取时间
            if (!time) {
              const text = $item.text()
              const timeMatch = text.match(/(\d{4}[-\/]\d{1,2}[-\/]\d{1,2})/);
              if (timeMatch) {
                time = timeMatch[1]
              }
            }

            if (title && url) {
              items.push({
                title: title.substring(0, 100),
                url,
                time: time || new Date().toLocaleDateString("zh-CN"),
              })
              newsFound = true
            }
          } catch (error) {
            console.error("CAIVD行业单项解析错误:", error)
          }
        })

        if (newsFound) break
      }
    }

    // 如果没有找到新闻，尝试通用选择器
    if (!newsFound) {
      $("a").each((_, element) => {
        try {
          const $link = $(element)
          const title = $link.text().trim()
          const url = $link.attr("href")

          if (title && url && title.length > 10) {
            const hasKeyword = keywords.some(keyword => 
              title.toLowerCase().includes(keyword.toLowerCase())
            )
            
            if (hasKeyword && url.includes("article")) {
              let fullUrl = url
              if (!fullUrl.startsWith("http")) {
                fullUrl = fullUrl.startsWith("/") ? `https://www.caivd-org.cn${fullUrl}` : `https://www.caivd-org.cn/${fullUrl}`
              }

              items.push({
                title: title.substring(0, 100),
                url: fullUrl,
                time: new Date().toLocaleDateString("zh-CN"),
              })
            }
          }
        } catch (error) {
          console.error("CAIVD行业通用解析错误:", error)
        }
      })
    }

    console.log(`CAIVD行业获取到 ${items.length} 条新闻`)
    return items.slice(0, 15) // 限制返回数量

  } catch (error) {
    console.error("CAIVD行业获取失败:", error)
    return []
  }
})
