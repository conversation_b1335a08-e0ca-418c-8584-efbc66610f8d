import type { NewsItem } from "~/shared/types"
import { load } from "cheerio"

export default defineEventHandler(async (): Promise<NewsItem[]> => {
  try {
    const response = await $fetch("https://www.bioon.com/", {
      headers: {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
      },
    })

    const $ = load(response)
    const items: NewsItem[] = []

    // 生物医学相关关键词
    const keywords = [
      "生物", "医学", "药物", "基因", "蛋白质", "细胞", "分子", "临床", "治疗", "诊断",
      "疫苗", "抗体", "免疫", "癌症", "肿瘤", "疾病", "病毒", "细菌", "感染", "新药",
      "研发", "试验", "FDA", "NMPA", "审批", "上市", "生物技术", "制药", "医疗器械",
      "干细胞", "再生医学", "精准医学", "个性化", "靶向", "生物标志物", "组学",
      "基因组", "蛋白组", "代谢组", "转录组", "表观遗传", "CRISPR", "基因编辑",
      "生物制药", "单抗", "双抗", "ADC", "CAR-T", "细胞治疗", "基因治疗"
    ]

    // 尝试多种选择器策略
    const selectors = [
      ".news-list .news-item",
      ".article-list .article-item", 
      ".content-list .content-item",
      ".list-item",
      ".news-content .item",
      "article",
      ".post",
      ".entry"
    ]

    let newsFound = false

    for (const selector of selectors) {
      const elements = $(selector)
      if (elements.length > 0) {
        elements.each((_, element) => {
          try {
            const $item = $(element)
            
            // 提取标题
            let title = ""
            const titleSelectors = ["h1", "h2", "h3", "h4", ".title", ".headline", "a[title]", "a"]
            for (const titleSel of titleSelectors) {
              const titleEl = $item.find(titleSel).first()
              if (titleEl.length > 0) {
                title = titleEl.attr("title") || titleEl.text().trim()
                if (title) break
              }
            }

            if (!title || title.length < 10) return

            // 关键词过滤
            const hasKeyword = keywords.some(keyword => 
              title.toLowerCase().includes(keyword.toLowerCase())
            )
            if (!hasKeyword) return

            // 提取链接
            let url = ""
            const linkEl = $item.find("a").first()
            if (linkEl.length > 0) {
              url = linkEl.attr("href") || ""
              if (url && !url.startsWith("http")) {
                url = url.startsWith("/") ? `https://www.bioon.com${url}` : `https://www.bioon.com/${url}`
              }
            }

            if (!url) return

            // 提取时间
            let time = ""
            const timeSelectors = [".time", ".date", ".publish-time", ".created", "time", ".meta-time"]
            for (const timeSel of timeSelectors) {
              const timeEl = $item.find(timeSel).first()
              if (timeEl.length > 0) {
                time = timeEl.text().trim()
                if (time) break
              }
            }

            // 如果没有找到时间，尝试从文本中提取
            if (!time) {
              const text = $item.text()
              const timeMatch = text.match(/(\d{4}[-\/]\d{1,2}[-\/]\d{1,2}|\d{1,2}[-\/]\d{1,2}|\d{2}:\d{2})/);
              if (timeMatch) {
                time = timeMatch[1]
              }
            }

            if (title && url) {
              items.push({
                title: title.substring(0, 100),
                url,
                time: time || new Date().toLocaleDateString("zh-CN"),
              })
              newsFound = true
            }
          } catch (error) {
            console.error("生物谷单项解析错误:", error)
          }
        })

        if (newsFound) break
      }
    }

    // 如果没有找到新闻，尝试通用选择器
    if (!newsFound) {
      $("a").each((_, element) => {
        try {
          const $link = $(element)
          const title = $link.attr("title") || $link.text().trim()
          const url = $link.attr("href")

          if (title && url && title.length > 10) {
            const hasKeyword = keywords.some(keyword => 
              title.toLowerCase().includes(keyword.toLowerCase())
            )
            
            if (hasKeyword) {
              let fullUrl = url
              if (!fullUrl.startsWith("http")) {
                fullUrl = fullUrl.startsWith("/") ? `https://www.bioon.com${fullUrl}` : `https://www.bioon.com/${fullUrl}`
              }

              items.push({
                title: title.substring(0, 100),
                url: fullUrl,
                time: new Date().toLocaleDateString("zh-CN"),
              })
            }
          }
        } catch (error) {
          console.error("生物谷通用解析错误:", error)
        }
      })
    }

    console.log(`生物谷获取到 ${items.length} 条新闻`)
    return items.slice(0, 20) // 限制返回数量

  } catch (error) {
    console.error("生物谷获取失败:", error)
    return []
  }
})
