import type { NewsItem } from "~/shared/types"
import { load } from "cheerio"

export default defineEventHandler(async (): Promise<NewsItem[]> => {
  try {
    const response = await $fetch("https://www.chinacdc.cn/zxyw/", {
      headers: {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
      },
    })

    const $ = load(response)
    const items: NewsItem[] = []

    // 疾控和公共卫生相关关键词
    const keywords = [
      "疾病", "预防", "控制", "疫情", "传染病", "公共卫生", "健康", "卫生",
      "流行病", "监测", "防控", "应急", "突发", "病毒", "细菌", "感染",
      "疫苗", "免疫", "接种", "规划", "慢性病", "营养", "环境", "职业",
      "放射", "中毒", "伤害", "安全", "风险", "评估", "调查", "研究",
      "科研", "技术", "标准", "指南", "规范", "培训", "教育", "宣传",
      "合作", "交流", "国际", "WHO", "世卫", "CDC", "疾控", "中心",
      "新冠", "COVID", "流感", "结核", "艾滋", "肝炎", "手足口", "登革热",
      "食品", "饮水", "空气", "土壤", "化学", "生物", "物理", "因子"
    ]

    // 尝试多种选择器策略
    const selectors = [
      "li a[href*='t2025']",
      "li a[href*='t2024']", 
      ".news-list li",
      ".article-list li",
      ".content-list li",
      "ul li a",
      "li",
      "a[href*='/202']"
    ]

    let newsFound = false

    for (const selector of selectors) {
      const elements = $(selector)
      if (elements.length > 0) {
        elements.each((_, element) => {
          try {
            const $item = $(element)
            
            // 提取标题和链接
            let title = ""
            let url = ""
            
            if ($item.is("a")) {
              title = $item.text().trim()
              url = $item.attr("href") || ""
            } else {
              const linkEl = $item.find("a").first()
              if (linkEl.length > 0) {
                title = linkEl.text().trim()
                url = linkEl.attr("href") || ""
              }
            }

            if (!title || title.length < 8 || !url) return

            // 关键词过滤
            const hasKeyword = keywords.some(keyword => 
              title.toLowerCase().includes(keyword.toLowerCase())
            )
            if (!hasKeyword) return

            // 处理相对URL
            if (url && !url.startsWith("http")) {
              url = url.startsWith("/") ? `https://www.chinacdc.cn${url}` : `https://www.chinacdc.cn/${url}`
            }

            // 提取时间
            let time = ""
            const timeSelectors = [".time", ".date", ".publish-time", ".created", "time"]
            for (const timeSel of timeSelectors) {
              const timeEl = $item.find(timeSel).first()
              if (timeEl.length > 0) {
                time = timeEl.text().trim()
                if (time) break
              }
            }

            // 从URL或文本中提取时间
            if (!time) {
              const urlTimeMatch = url.match(/t(\d{4})(\d{2})(\d{2})/);
              if (urlTimeMatch) {
                time = `${urlTimeMatch[1]}-${urlTimeMatch[2]}-${urlTimeMatch[3]}`
              } else {
                const text = $item.text()
                const timeMatch = text.match(/(\d{4}[-\/]\d{1,2}[-\/]\d{1,2})/);
                if (timeMatch) {
                  time = timeMatch[1]
                }
              }
            }

            if (title && url) {
              items.push({
                title: title.substring(0, 100),
                url,
                time: time || new Date().toLocaleDateString("zh-CN"),
              })
              newsFound = true
            }
          } catch (error) {
            console.error("中国疾控中心单项解析错误:", error)
          }
        })

        if (newsFound) break
      }
    }

    // 如果没有找到新闻，尝试通用选择器
    if (!newsFound) {
      $("a").each((_, element) => {
        try {
          const $link = $(element)
          const title = $link.text().trim()
          const url = $link.attr("href")

          if (title && url && title.length > 8) {
            const hasKeyword = keywords.some(keyword => 
              title.toLowerCase().includes(keyword.toLowerCase())
            )
            
            if (hasKeyword && (url.includes("/202") || url.includes("t202"))) {
              let fullUrl = url
              if (!fullUrl.startsWith("http")) {
                fullUrl = fullUrl.startsWith("/") ? `https://www.chinacdc.cn${fullUrl}` : `https://www.chinacdc.cn/${fullUrl}`
              }

              // 从URL提取时间
              let time = ""
              const urlTimeMatch = fullUrl.match(/t(\d{4})(\d{2})(\d{2})/);
              if (urlTimeMatch) {
                time = `${urlTimeMatch[1]}-${urlTimeMatch[2]}-${urlTimeMatch[3]}`
              }

              items.push({
                title: title.substring(0, 100),
                url: fullUrl,
                time: time || new Date().toLocaleDateString("zh-CN"),
              })
            }
          }
        } catch (error) {
          console.error("中国疾控中心通用解析错误:", error)
        }
      })
    }

    console.log(`中国疾控中心获取到 ${items.length} 条新闻`)
    return items.slice(0, 20) // 限制返回数量

  } catch (error) {
    console.error("中国疾控中心获取失败:", error)
    return []
  }
})
