version: '3'

services:
  newsnow:
    image: ghcr.io/ourongxing/newsnow:latest
    container_name: newsnow
    ports:
      - '4444:4444'
    volumes:
      - newsnow_data:/usr/app/.data
    environment:
      - HOST=0.0.0.0
      - PORT=4444
      - NODE_ENV=production
      - G_CLIENT_ID=
      - G_CLIENT_SECRET=
      - JWT_SECRET=
      - INIT_TABLE=true
      - ENABLE_CACHE=true

volumes:
  newsnow_data:
    name: newsnow_data
