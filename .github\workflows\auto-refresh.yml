name: Auto Refresh

on:
  schedule:
    - cron: '*/20 0-17,22,23 * * *'
  workflow_dispatch:

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: lts/*

      - name: Refresh
        env:
          JWT_TOKEN: ${{ secrets.JWT_TOKEN }}
        run: npx tsx ./scripts/refresh.ts
