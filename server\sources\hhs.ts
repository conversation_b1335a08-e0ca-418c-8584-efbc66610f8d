import type { NewsItem } from "~/shared/types"
import { load } from "cheerio"

export default defineEventHandler(async (): Promise<NewsItem[]> => {
  try {
    const response = await $fetch("https://www.hhs.gov/", {
      headers: {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7",
      },
    })

    const $ = load(response)
    const items: NewsItem[] = []

    // 尝试多种选择器来获取HHS新闻内容
    const selectors = [
      ".news-list li",
      ".list li",
      "ul li",
      ".item",
      ".news-item",
      "article",
      ".content-item"
    ]

    for (const selector of selectors) {
      $(selector).each((_, element) => {
        const $item = $(element)
        
        // 获取链接和标题
        const linkElement = $item.find("a").first()
        const href = linkElement.attr("href")
        let title = linkElement.text().trim()
        
        // 如果没有找到标题，尝试其他方式
        if (!title) {
          title = $item.find(".title, .headline, h1, h2, h3, h4").text().trim()
        }
        
        // 获取时间信息
        const timeElement = $item.find(".time, .date, [class*='time'], [class*='date']")
        let timeText = timeElement.text().trim()
        
        // 如果没有找到时间，尝试从文本中提取
        if (!timeText) {
          const fullText = $item.text()
          const dateMatch = fullText.match(/(\d{4}-\d{2}-\d{2}|\w+ \d{1,2}, \d{4})/)
          if (dateMatch) {
            timeText = dateMatch[1]
          }
        }
        
        if (title && href && title.length > 5) {
          // 构建完整URL
          let url = href
          if (href.startsWith("/")) {
            url = `https://www.hhs.gov${href}`
          } else if (!href.startsWith("http")) {
            url = `https://www.hhs.gov/${href}`
          }
          
          // 解析时间
          let pubDate = Date.now()
          if (timeText) {
            try {
              if (timeText.match(/\d{4}-\d{2}-\d{2}/)) {
                pubDate = new Date(timeText).getTime()
              } else if (timeText.match(/\w+ \d{1,2}, \d{4}/)) {
                pubDate = new Date(timeText).getTime()
              }
            } catch (e) {
              // 如果解析失败，使用当前时间
              pubDate = Date.now()
            }
          }

          // 过滤相关内容
          const relevantKeywords = ["health", "medical", "device", "drug", "FDA", "policy", "regulation", "healthcare", "medicine", "clinical", "safety", "approval"]
          const isRelevant = relevantKeywords.some(keyword => 
            title.toLowerCase().includes(keyword.toLowerCase())
          )
          
          if (isRelevant) {
            // 检查是否已存在
            const exists = items.some(item => item.url === url)
            if (!exists) {
              items.push({
                id: url,
                title,
                url,
                pubDate,
                extra: {
                  date: pubDate,
                },
              })
            }
          }
        }
      })
      
      if (items.length > 0) break
    }

    // 如果没有找到内容，尝试通用链接选择器
    if (items.length === 0) {
      $("a").each((_, element) => {
        const $item = $(element)
        const href = $item.attr("href")
        const title = $item.text().trim()
        
        // 过滤相关内容
        const relevantKeywords = ["health", "medical", "device", "drug", "FDA", "policy", "regulation", "healthcare", "medicine", "clinical", "safety", "approval"]
        const isRelevant = relevantKeywords.some(keyword => 
          title.toLowerCase().includes(keyword.toLowerCase())
        )
        
        if (title && href && title.length > 10 && isRelevant) {
          let url = href
          if (href.startsWith("/")) {
            url = `https://www.hhs.gov${href}`
          }
          
          const exists = items.some(item => item.url === url)
          if (!exists) {
            items.push({
              id: url,
              title,
              url,
              pubDate: Date.now(),
              extra: {
                date: Date.now(),
              },
            })
          }
        }
      })
    }

    console.log(`美国HHS: 获取到 ${items.length} 条卫生政策`)
    return items.slice(0, 20) // 限制返回20条
    
  } catch (error) {
    console.error("美国HHS获取失败:", error)
    return []
  }
})
