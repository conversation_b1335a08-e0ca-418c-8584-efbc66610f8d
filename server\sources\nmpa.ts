import * as cheerio from "cheerio"
import type { NewsItem } from "@shared/types"

export default defineSource(async () => {
  try {
    // 使用政府网站的搜索功能来获取药监相关新闻
    const searchUrl = "https://sousuo.gov.cn/column/30611/0.htm"
    const html: any = await myFetch(searchUrl)
    const $ = cheerio.load(html)
    const news: NewsItem[] = []

    // 抓取所有链接，然后过滤药监相关内容
    $("a").each((_, el) => {
      const $a = $(el)
      const url = $a.attr("href")
      const title = $a.text().trim()

      // 查找包含药品、医疗器械、化妆品、药监等关键词的新闻
      if (url && title && title.length > 8 &&
          (title.includes("药品") || title.includes("医疗器械") || title.includes("化妆品") ||
           title.includes("药监") || title.includes("NMPA") || title.includes("国家药监局") ||
           title.includes("药物") || title.includes("疫苗") || title.includes("医药") ||
           title.includes("药品监督") || title.includes("医疗器械监督"))) {

        // 处理相对URL
        const fullUrl = url.startsWith("http") ? url : `https://www.gov.cn${url}`

        // 尝试从父元素或兄弟元素获取日期信息
        const $parent = $a.parent()
        const dateText = $parent.find(".time, .date, span").text().trim() ||
                       $parent.siblings().find(".time, .date").text().trim() ||
                       $a.siblings(".time, .date").text().trim()

        // 解析日期
        let pubDate: number | undefined
        if (dateText) {
          const dateMatch = dateText.match(/(\d{4})[/-](\d{1,2})[/-](\d{1,2})/)
          if (dateMatch) {
            const [, year, month, day] = dateMatch
            pubDate = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`).getTime()
          }
        }

        news.push({
          id: fullUrl,
          title: title.trim(),
          url: fullUrl,
          pubDate,
          extra: {
            date: pubDate || Date.now(),
          },
        })
      }
    })

    // 如果没有找到足够的内容，尝试其他政府网站页面
    if (news.length < 5) {
      try {
        const additionalUrls = [
          "https://www.gov.cn/zhengce/",
          "https://www.gov.cn/yaowen/"
        ]

        for (const additionalUrl of additionalUrls) {
          const additionalHtml: any = await myFetch(additionalUrl)
          const $additional = cheerio.load(additionalHtml)

          $additional("a").each((_, el) => {
            const $a = $additional(el)
            const url = $a.attr("href")
            const title = $a.text().trim()

            if (url && title && title.length > 8 &&
                (title.includes("药品") || title.includes("医疗器械") || title.includes("化妆品") ||
                 title.includes("药监") || title.includes("NMPA") || title.includes("国家药监局"))) {

              const fullUrl = url.startsWith("http") ? url : `https://www.gov.cn${url}`

              // 避免重复
              if (!news.find(item => item.id === fullUrl)) {
                news.push({
                  id: fullUrl,
                  title: title.trim(),
                  url: fullUrl,
                  extra: {
                    date: Date.now(),
                  },
                })
              }
            }
          })

          if (news.length >= 10) break
        }
      } catch (e) {
        console.log("Failed to fetch additional NMPA content:", e)
      }
    }

    return news.slice(0, 20).sort((a, b) => (b.pubDate || b.extra?.date || 0) - (a.pubDate || a.extra?.date || 0))

  } catch (error) {
    console.error("NMPA fetch error:", error)
    return []
  }
})
