import * as cheerio from "cheerio"
import type { NewsItem } from "@shared/types"

export default defineSource(async () => {
  try {
    // 抓取 NMPA 医疗器械政策解读页面
    const html: any = await myFetch("https://www.nmpa.gov.cn/ylqx/ylqxzhcjd/index.html")
    const $ = cheerio.load(html)
    const news: NewsItem[] = []
    
    // 尝试多种可能的选择器来匹配页面结构
    const selectors = [
      ".list_01 li",
      ".list li", 
      ".news-list li",
      ".content-list li",
      "ul li",
      ".article-list li",
      ".main-content li"
    ]
    
    let found = false
    for (const selector of selectors) {
      const $items = $(selector)
      if ($items.length > 0) {
        $items.each((_, el) => {
          const $el = $(el)
          const $a = $el.find("a").first()
          const url = $a.attr("href")
          const title = $a.attr("title") || $a.text().trim()
          
          if (url && title && title.length > 5) {
            // 处理相对URL
            const fullUrl = url.startsWith("http") ? url : `https://www.nmpa.gov.cn${url}`
            
            // 尝试从父元素或兄弟元素获取日期信息
            const $parent = $el
            const dateText = $parent.find(".time, .date, span").text().trim() || 
                           $parent.siblings().find(".time, .date").text().trim() ||
                           $a.siblings(".time, .date").text().trim()
            
            // 解析日期
            let pubDate: number | undefined
            if (dateText) {
              const dateMatch = dateText.match(/(\d{4})[/-](\d{1,2})[/-](\d{1,2})/)
              if (dateMatch) {
                const [, year, month, day] = dateMatch
                pubDate = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`).getTime()
              }
            }
            
            news.push({
              id: fullUrl,
              title: title.trim(),
              url: fullUrl,
              pubDate,
              extra: {
                date: pubDate || Date.now(),
              },
            })
            found = true
          }
        })
        
        if (found && news.length > 0) break
      }
    }
    
    // 如果没有找到内容，尝试通用选择器
    if (news.length === 0) {
      $("a").each((_, el) => {
        const $a = $(el)
        const url = $a.attr("href")
        const title = $a.text().trim()
        
        // 查找包含政策解读相关关键词的链接
        if (url && title && title.length > 8 && 
            (title.includes("解读") || title.includes("政策") || title.includes("解释") || 
             title.includes("说明") || title.includes("医疗器械") || title.includes("指导"))) {
          
          const fullUrl = url.startsWith("http") ? url : `https://www.nmpa.gov.cn${url}`
          
          // 避免重复
          if (!news.find(item => item.id === fullUrl)) {
            news.push({
              id: fullUrl,
              title: title.trim(),
              url: fullUrl,
              extra: {
                date: Date.now(),
              },
            })
          }
        }
      })
    }
    
    return news.slice(0, 20).sort((a, b) => (b.pubDate || b.extra?.date || 0) - (a.pubDate || a.extra?.date || 0))
    
  } catch (error) {
    console.error("NMPA 政策解读 fetch error:", error)
    return []
  }
})
