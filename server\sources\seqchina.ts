import type { NewsItem } from "~/shared/types"
import { load } from "cheerio"

export default defineEventHandler(async (): Promise<NewsItem[]> => {
  try {
    const response = await $fetch("https://www.seqchina.cn/", {
      headers: {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
      },
    })

    const $ = load(response)
    const items: NewsItem[] = []

    // 基因测序和生物技术相关关键词
    const keywords = [
      "测序", "基因", "DNA", "RNA", "基因组", "外显子", "全基因组", "转录组", "表观遗传",
      "单细胞", "空间转录组", "蛋白组", "代谢组", "多组学", "生物信息", "CRISPR", "基因编辑",
      "精准医学", "个性化医疗", "液体活检", "ctDNA", "无创", "产前筛查", "遗传病",
      "肿瘤", "癌症", "免疫", "CAR-T", "细胞治疗", "基因治疗", "生物标志物",
      "诊断", "检测", "试剂盒", "仪器", "平台", "技术", "算法", "数据库",
      "临床", "研究", "发现", "突破", "创新", "发表", "论文", "Nature", "Science", "Cell"
    ]

    // 尝试多种选择器策略
    const selectors = [
      ".news-list .news-item",
      ".article-list .article-item", 
      ".content-list .content-item",
      ".list-item",
      ".news-content .item",
      "article",
      ".post",
      ".entry",
      ".item-wrap",
      ".news-wrap"
    ]

    let newsFound = false

    for (const selector of selectors) {
      const elements = $(selector)
      if (elements.length > 0) {
        elements.each((_, element) => {
          try {
            const $item = $(element)
            
            // 提取标题
            let title = ""
            const titleSelectors = ["h1", "h2", "h3", "h4", ".title", ".headline", "a[title]", "a"]
            for (const titleSel of titleSelectors) {
              const titleEl = $item.find(titleSel).first()
              if (titleEl.length > 0) {
                title = titleEl.attr("title") || titleEl.text().trim()
                if (title) break
              }
            }

            if (!title || title.length < 8) return

            // 关键词过滤
            const hasKeyword = keywords.some(keyword => 
              title.toLowerCase().includes(keyword.toLowerCase())
            )
            if (!hasKeyword) return

            // 提取链接
            let url = ""
            const linkEl = $item.find("a").first()
            if (linkEl.length > 0) {
              url = linkEl.attr("href") || ""
              if (url && !url.startsWith("http")) {
                url = url.startsWith("/") ? `https://www.seqchina.cn${url}` : `https://www.seqchina.cn/${url}`
              }
            }

            if (!url) return

            // 提取时间
            let time = ""
            const timeSelectors = [".time", ".date", ".publish-time", ".created", "time", ".meta-time", ".post-date"]
            for (const timeSel of timeSelectors) {
              const timeEl = $item.find(timeSel).first()
              if (timeEl.length > 0) {
                time = timeEl.text().trim()
                if (time) break
              }
            }

            // 如果没有找到时间，尝试从文本中提取
            if (!time) {
              const text = $item.text()
              const timeMatch = text.match(/(\d{4}[-\/]\d{1,2}[-\/]\d{1,2}|\d{1,2}[-\/]\d{1,2}|\d{2}:\d{2})/);
              if (timeMatch) {
                time = timeMatch[1]
              }
            }

            if (title && url) {
              items.push({
                title: title.substring(0, 100),
                url,
                time: time || new Date().toLocaleDateString("zh-CN"),
              })
              newsFound = true
            }
          } catch (error) {
            console.error("测序中国单项解析错误:", error)
          }
        })

        if (newsFound) break
      }
    }

    // 如果没有找到新闻，尝试通用选择器
    if (!newsFound) {
      $("a").each((_, element) => {
        try {
          const $link = $(element)
          const title = $link.attr("title") || $link.text().trim()
          const url = $link.attr("href")

          if (title && url && title.length > 8) {
            const hasKeyword = keywords.some(keyword => 
              title.toLowerCase().includes(keyword.toLowerCase())
            )
            
            if (hasKeyword) {
              let fullUrl = url
              if (!fullUrl.startsWith("http")) {
                fullUrl = fullUrl.startsWith("/") ? `https://www.seqchina.cn${fullUrl}` : `https://www.seqchina.cn/${fullUrl}`
              }

              items.push({
                title: title.substring(0, 100),
                url: fullUrl,
                time: new Date().toLocaleDateString("zh-CN"),
              })
            }
          }
        } catch (error) {
          console.error("测序中国通用解析错误:", error)
        }
      })
    }

    console.log(`测序中国获取到 ${items.length} 条新闻`)
    return items.slice(0, 20) // 限制返回数量

  } catch (error) {
    console.error("测序中国获取失败:", error)
    return []
  }
})
