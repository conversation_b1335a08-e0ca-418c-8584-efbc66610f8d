import type { NewsItem } from "~/shared/types"
import { load } from "cheerio"

export default defineEventHandler(async (): Promise<NewsItem[]> => {
  try {
    const response = await $fetch("https://readhub.cn/medical", {
      headers: {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
      },
    })

    const $ = load(response)
    const items: NewsItem[] = []

    // 尝试多种选择器来获取医疗产业内容
    const selectors = [
      ".topic-item",
      ".news-item", 
      ".item",
      "article",
      "[class*='topic']",
      "[class*='news']"
    ]

    for (const selector of selectors) {
      $(selector).each((_, element) => {
        const $item = $(element)
        
        // 获取链接和标题
        const linkElement = $item.find("a").first()
        const href = linkElement.attr("href")
        let title = linkElement.text().trim()
        
        // 如果没有找到标题，尝试其他方式
        if (!title) {
          title = $item.find("h1, h2, h3, h4, .title").text().trim()
        }
        
        // 获取时间信息
        const timeElement = $item.find(".time, .date, [class*='time'], [class*='date']")
        const timeText = timeElement.text().trim()
        
        if (title && href && title.length > 5) {
          // 构建完整URL
          const url = href.startsWith("/") ? `https://readhub.cn${href}` : href
          
          // 解析时间
          let pubDate = Date.now()
          if (timeText) {
            const timeMatch = timeText.match(/(\d+)\s*小时前/)
            if (timeMatch) {
              const hoursAgo = parseInt(timeMatch[1])
              pubDate = Date.now() - (hoursAgo * 60 * 60 * 1000)
            }
          }

          // 检查是否已存在
          const exists = items.some(item => item.url === url)
          if (!exists) {
            items.push({
              id: url,
              title,
              url,
              pubDate,
              extra: {
                date: pubDate,
              },
            })
          }
        }
      })
      
      if (items.length > 0) break
    }

    // 如果仍然没有找到内容，尝试通用链接选择器
    if (items.length === 0) {
      $("a").each((_, element) => {
        const $item = $(element)
        const href = $item.attr("href")
        const title = $item.text().trim()
        
        // 过滤医疗相关内容
        const medicalKeywords = ["医疗", "医药", "健康", "医院", "药品", "器械", "诊断", "治疗", "临床", "生物"]
        const isRelevant = medicalKeywords.some(keyword => title.includes(keyword))
        
        if (title && href && title.length > 10 && isRelevant) {
          const url = href.startsWith("/") ? `https://readhub.cn${href}` : href
          
          const exists = items.some(item => item.url === url)
          if (!exists) {
            items.push({
              id: url,
              title,
              url,
              pubDate: Date.now(),
              extra: {
                date: Date.now(),
              },
            })
          }
        }
      })
    }

    console.log(`Readhub医疗: 获取到 ${items.length} 条新闻`)
    return items.slice(0, 20) // 限制返回20条
    
  } catch (error) {
    console.error("Readhub医疗获取失败:", error)
    return []
  }
})
