import process from "node:process"
import { Interval } from "./consts"
import { typeSafeObjectFromEntries } from "./type.util"
import type { OriginSource, Source, SourceID } from "./types"

const Time = {
  Test: 1,
  Realtime: 2 * 60 * 1000,
  Fast: 5 * 60 * 1000,
  Default: Interval, // 10min
  Common: 30 * 60 * 1000,
  Slow: 60 * 60 * 1000,
}

export const originSources = {
  "nmpa": {
    name: "国家药监局",
    column: "china",
    color: "blue",
    interval: Time.Common,
    title: "要闻",
    home: "https://www.nmpa.gov.cn",
    type: "realtime",
  },
  "nmpa_jgdt": {
    name: "NMPA医疗器械",
    column: "china",
    color: "blue",
    interval: Time.Common,
    title: "监管工作",
    home: "https://www.nmpa.gov.cn/ylqx/ylqxjgdt/index.html",
    type: "realtime",
  },
  "nmpa_ggtg": {
    name: "NMPA医疗器械",
    column: "china",
    color: "blue",
    interval: Time.Common,
    title: "公告通告",
    home: "https://www.nmpa.gov.cn/ylqx/ylqxggtg/index.html",
    type: "realtime",
  },
  "nmpa_fgwj": {
    name: "NMPA医疗器械",
    column: "china",
    color: "blue",
    interval: Time.Common,
    title: "法规文件",
    home: "https://www.nmpa.gov.cn/ylqx/ylqxfgwj/index.html",
    type: "realtime",
  },
  "nmpa_zhcjd": {
    name: "NMPA医疗器械",
    column: "china",
    color: "blue",
    interval: Time.Common,
    title: "政策解读",
    home: "https://www.nmpa.gov.cn/ylqx/ylqxzhcjd/index.html",
    type: "realtime",
  },
  "nmpa_fxjch": {
    name: "NMPA医疗器械",
    column: "china",
    color: "blue",
    interval: Time.Common,
    title: "飞行检查",
    home: "https://www.nmpa.gov.cn/xxgk/fxjzh/ylqxfxjch/index.html",
    type: "realtime",
  },
  "nmpa_zhh": {
    name: "NMPA医疗器械",
    column: "china",
    color: "blue",
    interval: Time.Common,
    title: "器械召回",
    home: "https://www.nmpa.gov.cn/xxgk/chpzhh/ylqxzhh/index.html",
    type: "realtime",
  },
  "readhub_hot": {
    name: "Readhub热榜",
    column: "tech",
    color: "orange",
    interval: Time.Common,
    title: "24小时热榜",
    home: "https://readhub.cn/hot?type=daily",
    type: "realtime",
  },
  "readhub_medical": {
    name: "Readhub医疗",
    column: "tech",
    color: "orange",
    interval: Time.Common,
    title: "医疗产业",
    home: "https://readhub.cn/medical",
    type: "realtime",
  },
  "vbdata": {
    name: "动脉网",
    column: "tech",
    color: "green",
    interval: Time.Common,
    title: "7x24H情报",
    home: "https://www.vbdata.cn/intelList",
    type: "realtime",
  },
  "nhc": {
    name: "国家卫健委",
    column: "china",
    color: "red",
    interval: Time.Common,
    title: "工作通知",
    home: "https://www.nhc.gov.cn/wjw/gztz/list.shtml",
    type: "realtime",
  },
  "samr": {
    name: "市场监管总局",
    column: "china",
    color: "red",
    interval: Time.Common,
    title: "通知公告",
    home: "https://www.samr.gov.cn/jls/tzgg/index.html",
    type: "realtime",
  },
  "cmde": {
    name: "CMDE",
    column: "china",
    color: "blue",
    interval: Time.Common,
    title: "审评报告",
    home: "https://www.cmde.org.cn/xwdt/shpbg/index.html",
    type: "realtime",
  },
  "cnpharm": {
    name: "中国药闻",
    column: "china",
    color: "purple",
    interval: Time.Common,
    title: "器械监管",
    home: "https://www.cnpharm.com/ylqx/jxjg/",
    type: "realtime",
  },
  "most": {
    name: "科技部",
    column: "china",
    color: "red",
    interval: Time.Common,
    title: "科技计划",
    home: "https://service.most.gov.cn/kjjh_tztg_all/?type=0",
    type: "realtime",
  },
  "innomd": {
    name: "创新医疗器械网",
    column: "tech",
    color: "cyan",
    interval: Time.Common,
    title: "行业新闻",
    home: "https://www.innomd.org/news.html",
    type: "realtime",
  },
  "camdi_news": {
    name: "CAMDI",
    column: "tech",
    color: "cyan",
    interval: Time.Common,
    title: "新闻动态",
    home: "http://www.camdi.cn/news/list/50,62,0",
    type: "realtime",
  },
  "camdi_activity": {
    name: "CAMDI",
    column: "tech",
    color: "cyan",
    interval: Time.Common,
    title: "活动会议",
    home: "http://www.camdi.cn/activity/list",
    type: "realtime",
  },
  "instrument": {
    name: "仪器信息网",
    column: "tech",
    color: "teal",
    interval: Time.Common,
    title: "行业资讯",
    home: "https://www.instrument.com.cn/news/",
    type: "realtime",
  },
  "hbba": {
    name: "标准化协会",
    column: "china",
    color: "gray",
    interval: Time.Common,
    title: "医药标准",
    home: "https://hbba.sacinfo.org.cn/stdList?key=&trade=%E5%8C%BB%E8%8D%AF",
    type: "realtime",
  },
  "hhs": {
    name: "美国HHS",
    column: "world",
    color: "blue",
    interval: Time.Common,
    title: "卫生政策",
    home: "https://www.hhs.gov/",
    type: "realtime",
  },

  "bioon": {
    name: "生物谷",
    column: "tech",
    color: "green",
    interval: Time.Common,
    title: "生物医学",
    home: "https://www.bioon.com/",
    type: "realtime",
  },

  "seqchina": {
    name: "测序中国",
    column: "tech",
    color: "blue",
    interval: Time.Common,
    title: "基因测序",
    home: "https://www.seqchina.cn/",
    type: "realtime",
  },

  "biodiscover": {
    name: "生物探索",
    column: "tech",
    color: "purple",
    interval: Time.Common,
    title: "生物科技",
    home: "https://www.biodiscover.com/",
    type: "realtime",
  },

  "caivd_zhuanlan": {
    name: "CAIVD专栏",
    column: "tech",
    color: "orange",
    interval: Time.Common,
    title: "行业专栏",
    home: "https://www.caivd-org.cn/zhuanlan.asp",
    type: "realtime",
  },

  "caivd_hangye": {
    name: "CAIVD行业",
    column: "tech",
    color: "red",
    interval: Time.Common,
    title: "行业资讯",
    home: "https://www.caivd-org.cn/hangye.asp",
    type: "realtime",
  },

  "exosomemed": {
    name: "外泌体之家",
    column: "tech",
    color: "cyan",
    interval: Time.Common,
    title: "外泌体研究",
    home: "https://www.exosomemed.com/",
    type: "realtime",
  },

  "chinacdc": {
    name: "中国疾控中心",
    column: "china",
    color: "red",
    interval: Time.Common,
    title: "疾控资讯",
    home: "https://www.chinacdc.cn/zxyw/",
    type: "realtime",
  },
} as const satisfies Record<string, OriginSource>

export function genSources() {
  const _: [SourceID, Source][] = []

  Object.entries(originSources).forEach(([id, source]: [any, OriginSource]) => {
    const parent = {
      name: source.name,
      type: source.type,
      disable: source.disable,
      desc: source.desc,
      column: source.column,
      home: source.home,
      color: source.color ?? "primary",
      interval: source.interval ?? Time.Default,
    }
    if (source.sub && Object.keys(source.sub).length) {
      Object.entries(source.sub).forEach(([subId, subSource], i) => {
        if (i === 0) {
          _.push([
            id,
            {
              redirect: `${id}-${subId}`,
              ...parent,
              ...subSource,
            },
          ] as [any, Source])
        }
        _.push([`${id}-${subId}`, { ...parent, ...subSource }] as [
          any,
          Source,
        ])
      })
    } else {
      _.push([
        id,
        {
          title: source.title,
          ...parent,
        },
      ])
    }
  })

  return typeSafeObjectFromEntries(
    _.filter(([_, v]) => {
      if (v.disable === "cf" && process.env.CF_PAGES) {
        return false
      } else if (v.disable === true) {
        return false
      } else {
        return true
      }
    }),
  )
}
