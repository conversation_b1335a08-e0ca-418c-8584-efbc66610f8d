import process from "node:process"
import { Interval } from "./consts"
import { typeSafeObjectFromEntries } from "./type.util"
import type { OriginSource, Source, SourceID } from "./types"

const Time = {
  Test: 1,
  Realtime: 2 * 60 * 1000,
  Fast: 5 * 60 * 1000,
  Default: Interval, // 10min
  Common: 30 * 60 * 1000,
  Slow: 60 * 60 * 1000,
}

export const originSources = {
  "nmpa": {
    name: "国家药监局",
    column: "china",
    color: "blue",
    interval: Time.Common,
    title: "要闻",
    home: "https://www.nmpa.gov.cn",
    type: "realtime",
  },
  "nmpa_jgdt": {
    name: "NMPA医疗器械",
    column: "china",
    color: "blue",
    interval: Time.Common,
    title: "监管工作",
    home: "https://www.nmpa.gov.cn/ylqx/ylqxjgdt/index.html",
    type: "realtime",
  },
  "nmpa_ggtg": {
    name: "NMPA医疗器械",
    column: "china",
    color: "blue",
    interval: Time.Common,
    title: "公告通告",
    home: "https://www.nmpa.gov.cn/ylqx/ylqxggtg/index.html",
    type: "realtime",
  },
  "nmpa_fgwj": {
    name: "NMPA医疗器械",
    column: "china",
    color: "blue",
    interval: Time.Common,
    title: "法规文件",
    home: "https://www.nmpa.gov.cn/ylqx/ylqxfgwj/index.html",
    type: "realtime",
  },
  "nmpa_zhcjd": {
    name: "NMPA医疗器械",
    column: "china",
    color: "blue",
    interval: Time.Common,
    title: "政策解读",
    home: "https://www.nmpa.gov.cn/ylqx/ylqxzhcjd/index.html",
    type: "realtime",
  },
  "nmpa_fxjch": {
    name: "NMPA医疗器械",
    column: "china",
    color: "blue",
    interval: Time.Common,
    title: "飞行检查",
    home: "https://www.nmpa.gov.cn/xxgk/fxjzh/ylqxfxjch/index.html",
    type: "realtime",
  },
  "nmpa_zhh": {
    name: "NMPA医疗器械",
    column: "china",
    color: "blue",
    interval: Time.Common,
    title: "器械召回",
    home: "https://www.nmpa.gov.cn/xxgk/chpzhh/ylqxzhh/index.html",
    type: "realtime",
  },
} as const satisfies Record<string, OriginSource>

export function genSources() {
  const _: [SourceID, Source][] = []

  Object.entries(originSources).forEach(([id, source]: [any, OriginSource]) => {
    const parent = {
      name: source.name,
      type: source.type,
      disable: source.disable,
      desc: source.desc,
      column: source.column,
      home: source.home,
      color: source.color ?? "primary",
      interval: source.interval ?? Time.Default,
    }
    if (source.sub && Object.keys(source.sub).length) {
      Object.entries(source.sub).forEach(([subId, subSource], i) => {
        if (i === 0) {
          _.push([
            id,
            {
              redirect: `${id}-${subId}`,
              ...parent,
              ...subSource,
            },
          ] as [any, Source])
        }
        _.push([`${id}-${subId}`, { ...parent, ...subSource }] as [
          any,
          Source,
        ])
      })
    } else {
      _.push([
        id,
        {
          title: source.title,
          ...parent,
        },
      ])
    }
  })

  return typeSafeObjectFromEntries(
    _.filter(([_, v]) => {
      if (v.disable === "cf" && process.env.CF_PAGES) {
        return false
      } else if (v.disable === true) {
        return false
      } else {
        return true
      }
    }),
  )
}
