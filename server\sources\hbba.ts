import type { NewsItem } from "~/shared/types"
import { load } from "cheerio"

export default defineEventHandler(async (): Promise<NewsItem[]> => {
  try {
    const response = await $fetch("https://hbba.sacinfo.org.cn/stdList?key=&trade=%E5%8C%BB%E8%8D%AF", {
      headers: {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
      },
    })

    const $ = load(response)
    const items: NewsItem[] = []

    // 尝试多种选择器来获取医药标准内容
    const selectors = [
      ".std-list li",
      ".list li",
      "ul li",
      ".item",
      ".std-item",
      "tr"
    ]

    for (const selector of selectors) {
      $(selector).each((_, element) => {
        const $item = $(element)
        
        // 获取链接和标题
        const linkElement = $item.find("a").first()
        const href = linkElement.attr("href")
        let title = linkElement.text().trim()
        
        // 如果没有找到标题，尝试其他方式
        if (!title) {
          title = $item.find(".title, .headline, td").first().text().trim()
        }
        
        // 获取时间信息
        const timeElement = $item.find(".time, .date, [class*='time'], [class*='date']")
        let timeText = timeElement.text().trim()
        
        // 如果没有找到时间，尝试从文本中提取
        if (!timeText) {
          const fullText = $item.text()
          const dateMatch = fullText.match(/(\d{4}-\d{2}-\d{2}|\d{4}年\d{1,2}月\d{1,2}日)/)
          if (dateMatch) {
            timeText = dateMatch[1]
          }
        }
        
        if (title && href && title.length > 5) {
          // 构建完整URL
          let url = href
          if (href.startsWith("/")) {
            url = `https://hbba.sacinfo.org.cn${href}`
          } else if (!href.startsWith("http")) {
            url = `https://hbba.sacinfo.org.cn/${href}`
          }
          
          // 解析时间
          let pubDate = Date.now()
          if (timeText) {
            try {
              if (timeText.includes("年") && timeText.includes("月")) {
                // 处理中文日期格式
                const dateStr = timeText.replace(/年/g, "-").replace(/月/g, "-").replace(/日/g, "")
                pubDate = new Date(dateStr).getTime()
              } else if (timeText.match(/\d{4}-\d{2}-\d{2}/)) {
                pubDate = new Date(timeText).getTime()
              }
            } catch (e) {
              // 如果解析失败，使用当前时间
              pubDate = Date.now()
            }
          }

          // 过滤相关内容
          const relevantKeywords = ["医药", "医疗", "器械", "标准", "规范", "技术", "质量", "安全", "检测", "认证", "GB", "YY"]
          const isRelevant = relevantKeywords.some(keyword => title.includes(keyword))
          
          if (isRelevant) {
            // 检查是否已存在
            const exists = items.some(item => item.url === url)
            if (!exists) {
              items.push({
                id: url,
                title,
                url,
                pubDate,
                extra: {
                  date: pubDate,
                },
              })
            }
          }
        }
      })
      
      if (items.length > 0) break
    }

    console.log(`标准化协会: 获取到 ${items.length} 条医药标准`)
    return items.slice(0, 20) // 限制返回20条
    
  } catch (error) {
    console.error("标准化协会获取失败:", error)
    return []
  }
})
