import type { NewsItem } from "~/shared/types"
import { load } from "cheerio"

export default defineEventHandler(async (): Promise<NewsItem[]> => {
  try {
    const response = await $fetch("https://readhub.cn/hot?type=daily", {
      headers: {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
      },
    })

    const $ = load(response)
    const items: NewsItem[] = []

    // 解析热榜列表
    $("ol li").each((_, element) => {
      const $item = $(element)
      
      // 获取链接和标题
      const linkElement = $item.find("a")
      const href = linkElement.attr("href")
      const title = linkElement.text().trim()
      
      // 获取时间信息
      const timeText = $item.find("span").last().text().trim()
      
      if (title && href) {
        // 构建完整URL
        const url = href.startsWith("/") ? `https://readhub.cn${href}` : href
        
        // 解析时间
        let pubDate = Date.now()
        if (timeText) {
          const timeMatch = timeText.match(/(\d+)\s*小时前/)
          if (timeMatch) {
            const hoursAgo = parseInt(timeMatch[1])
            pubDate = Date.now() - (hoursAgo * 60 * 60 * 1000)
          }
        }

        items.push({
          id: url,
          title,
          url,
          pubDate,
          extra: {
            date: pubDate,
          },
        })
      }
    })

    // 如果没有找到内容，尝试其他选择器
    if (items.length === 0) {
      $("a[href*='/topic/']").each((_, element) => {
        const $item = $(element)
        const href = $item.attr("href")
        const title = $item.text().trim()
        
        if (title && href && title.length > 10) {
          const url = href.startsWith("/") ? `https://readhub.cn${href}` : href
          
          items.push({
            id: url,
            title,
            url,
            pubDate: Date.now(),
            extra: {
              date: Date.now(),
            },
          })
        }
      })
    }

    console.log(`Readhub热榜: 获取到 ${items.length} 条新闻`)
    return items.slice(0, 20) // 限制返回20条
    
  } catch (error) {
    console.error("Readhub热榜获取失败:", error)
    return []
  }
})
