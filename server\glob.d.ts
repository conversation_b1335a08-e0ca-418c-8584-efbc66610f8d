/* eslint-disable */

declare module 'glob:./sources/{*.ts,**/index.ts}' {
  export const _36kr: typeof import('./sources/_36kr')
  export const baidu: typeof import('./sources/baidu')
  export const bilibili: typeof import('./sources/bilibili')
  export const camdi_activity: typeof import('./sources/camdi_activity')
  export const camdi_news: typeof import('./sources/camdi_news')
  export const cankaoxiaoxi: typeof import('./sources/cankaoxiaoxi')
  export const chongbuluo: typeof import('./sources/chongbuluo')
  export const cls: typeof import('./sources/cls/index')
  export const cmde: typeof import('./sources/cmde')
  export const cnpharm: typeof import('./sources/cnpharm')
  export const coolapk: typeof import('./sources/coolapk/index')
  export const douyin: typeof import('./sources/douyin')
  export const fastbull: typeof import('./sources/fastbull')
  export const gelonghui: typeof import('./sources/gelonghui')
  export const ghxi: typeof import('./sources/ghxi')
  export const github: typeof import('./sources/github')
  export const hackernews: typeof import('./sources/hackernews')
  export const hbba: typeof import('./sources/hbba')
  export const hhs: typeof import('./sources/hhs')
  export const hupu: typeof import('./sources/hupu')
  export const ifeng: typeof import('./sources/ifeng')
  export const innomd: typeof import('./sources/innomd')
  export const instrument: typeof import('./sources/instrument')
  export const ithome: typeof import('./sources/ithome')
  export const jin10: typeof import('./sources/jin10')
  export const juejin: typeof import('./sources/juejin')
  export const kaopu: typeof import('./sources/kaopu')
  export const kuaishou: typeof import('./sources/kuaishou')
  export const linuxdo: typeof import('./sources/linuxdo')
  export const mktnews: typeof import('./sources/mktnews')
  export const most: typeof import('./sources/most')
  export const nhc: typeof import('./sources/nhc')
  export const nmpa: typeof import('./sources/nmpa')
  export const nmpa_fgwj: typeof import('./sources/nmpa_fgwj')
  export const nmpa_fxjch: typeof import('./sources/nmpa_fxjch')
  export const nmpa_ggtg: typeof import('./sources/nmpa_ggtg')
  export const nmpa_jgdt: typeof import('./sources/nmpa_jgdt')
  export const nmpa_zhcjd: typeof import('./sources/nmpa_zhcjd')
  export const nmpa_zhh: typeof import('./sources/nmpa_zhh')
  export const nowcoder: typeof import('./sources/nowcoder')
  export const pcbeta: typeof import('./sources/pcbeta')
  export const producthunt: typeof import('./sources/producthunt')
  export const readhub_hot: typeof import('./sources/readhub_hot')
  export const readhub_medical: typeof import('./sources/readhub_medical')
  export const samr: typeof import('./sources/samr')
  export const smzdm: typeof import('./sources/smzdm')
  export const solidot: typeof import('./sources/solidot')
  export const sputniknewscn: typeof import('./sources/sputniknewscn')
  export const sspai: typeof import('./sources/sspai')
  export const thepaper: typeof import('./sources/thepaper')
  export const tieba: typeof import('./sources/tieba')
  export const toutiao: typeof import('./sources/toutiao')
  export const v2ex: typeof import('./sources/v2ex')
  export const vbdata: typeof import('./sources/vbdata')
  export const wallstreetcn: typeof import('./sources/wallstreetcn')
  export const weibo: typeof import('./sources/weibo')
  export const xueqiu: typeof import('./sources/xueqiu')
  export const zaobao: typeof import('./sources/zaobao')
  export const zhihu: typeof import('./sources/zhihu')
}
