import type { NewsItem } from "~/shared/types"
import { load } from "cheerio"

export default defineEventHandler(async (): Promise<NewsItem[]> => {
  try {
    const response = await $fetch("https://www.vbdata.cn/intelList", {
      headers: {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
      },
    })

    const $ = load(response)
    const items: NewsItem[] = []

    // 尝试多种选择器来获取情报内容
    const selectors = [
      ".intel-item",
      ".news-item", 
      ".item",
      ".list-item",
      "[class*='intel']",
      "[class*='news']",
      "article"
    ]

    for (const selector of selectors) {
      $(selector).each((_, element) => {
        const $item = $(element)
        
        // 获取链接和标题
        const linkElement = $item.find("a").first()
        const href = linkElement.attr("href")
        let title = linkElement.text().trim()
        
        // 如果没有找到标题，尝试其他方式
        if (!title) {
          title = $item.find("h1, h2, h3, h4, .title, .headline").text().trim()
        }
        
        // 获取时间信息
        const timeElement = $item.find(".time, .date, [class*='time'], [class*='date']")
        const timeText = timeElement.text().trim()
        
        if (title && href && title.length > 5) {
          // 构建完整URL
          let url = href
          if (href.startsWith("/")) {
            url = `https://www.vbdata.cn${href}`
          } else if (!href.startsWith("http")) {
            url = `https://www.vbdata.cn/${href}`
          }
          
          // 解析时间
          let pubDate = Date.now()
          if (timeText) {
            // 尝试解析各种时间格式
            const timePatterns = [
              /(\d+)\s*小时前/,
              /(\d+)\s*分钟前/,
              /(\d{4}-\d{2}-\d{2})/,
              /(\d{2}:\d{2})/
            ]
            
            for (const pattern of timePatterns) {
              const match = timeText.match(pattern)
              if (match) {
                if (pattern.source.includes("小时前")) {
                  const hoursAgo = parseInt(match[1])
                  pubDate = Date.now() - (hoursAgo * 60 * 60 * 1000)
                } else if (pattern.source.includes("分钟前")) {
                  const minutesAgo = parseInt(match[1])
                  pubDate = Date.now() - (minutesAgo * 60 * 1000)
                }
                break
              }
            }
          }

          // 检查是否已存在
          const exists = items.some(item => item.url === url)
          if (!exists) {
            items.push({
              id: url,
              title,
              url,
              pubDate,
              extra: {
                date: pubDate,
              },
            })
          }
        }
      })
      
      if (items.length > 0) break
    }

    // 如果仍然没有找到内容，尝试通用链接选择器
    if (items.length === 0) {
      $("a").each((_, element) => {
        const $item = $(element)
        const href = $item.attr("href")
        const title = $item.text().trim()
        
        // 过滤医疗健康相关内容
        const relevantKeywords = ["医疗", "医药", "健康", "医院", "药品", "器械", "诊断", "治疗", "临床", "生物", "投融资", "创新", "科技"]
        const isRelevant = relevantKeywords.some(keyword => title.includes(keyword))
        
        if (title && href && title.length > 10 && isRelevant) {
          let url = href
          if (href.startsWith("/")) {
            url = `https://www.vbdata.cn${href}`
          }
          
          const exists = items.some(item => item.url === url)
          if (!exists) {
            items.push({
              id: url,
              title,
              url,
              pubDate: Date.now(),
              extra: {
                date: Date.now(),
              },
            })
          }
        }
      })
    }

    console.log(`动脉网: 获取到 ${items.length} 条情报`)
    return items.slice(0, 20) // 限制返回20条
    
  } catch (error) {
    console.error("动脉网获取失败:", error)
    return []
  }
})
